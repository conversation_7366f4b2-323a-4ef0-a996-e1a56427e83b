import React, { useState } from 'react';

const Onboarding = () => {
  const [name, setName] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [nameSubmitted, setNameSubmitted] = useState(false);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisComplete, setAnalysisComplete] = useState(false);
  const [showTrafficForm, setShowTrafficForm] = useState(false);
  const [monthlyVisitors, setMonthlyVisitors] = useState('');
  const [conversionRate, setConversionRate] = useState('');
  const [conversions, setConversions] = useState('');
  const [isSubmittingTraffic, setIsSubmittingTraffic] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) return;
    
    setIsSubmitting(true);
    
    // Simulate submission delay
    setTimeout(() => {
      setIsSubmitting(false);
      setNameSubmitted(true);
      
      // Start analyzing after showing the response
      setTimeout(() => {
        setIsAnalyzing(true);
        
        // Complete analysis after 3 seconds
        setTimeout(() => {
          setAnalysisComplete(true);
          
          // Show traffic form after analysis results
          setTimeout(() => {
            setShowTrafficForm(true);
          }, 1000);
        }, 3000);
      }, 1000);
    }, 500);
  };

  const handleTrafficSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!monthlyVisitors.trim() || !conversionRate.trim() || !conversions.trim()) return;
    
    setIsSubmittingTraffic(true);
    
    // Simulate processing
    setTimeout(() => {
      setIsSubmittingTraffic(false);
      // Handle next step - could redirect to dashboard or next onboarding step
      console.log('Traffic info submitted:', { monthlyVisitors, conversionRate, conversions });
      alert('Great! Your information has been submitted.');
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-[#2A2A2A]">
      {/* Top Navigation */}
      <nav className="flex items-center justify-between px-6 py-4 border-b border-gray-600">
        <div className="flex items-center gap-3">
          <div className="w-6 h-6 bg-white rounded flex items-center justify-center">
            <span className="text-black font-bold text-sm">V</span>
          </div>
          <span className="text-white text-lg font-medium">Variant AI</span>
        </div>
        <button className="text-gray-400 hover:text-white transition-colors">
          Logout
        </button>
      </nav>

      {/* Main Chat Interface */}
      <div className="flex items-center justify-center px-6 py-12">
        <div className="w-full max-w-2xl">
          {/* Chat Messages */}
          <div className="space-y-6">
            {/* Initial AI Message */}
            <div className="flex items-start gap-4">
              <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                  <span className="text-black font-bold text-sm">V</span>
                </div>
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-3">
                  <span className="text-white font-medium">Variant AI</span>
                </div>
                <div className="text-white text-base leading-relaxed mb-6">
                  <p className="mb-4">
                    I'm Variant, the AI agent for AB testing. I'm looking forward to working together.
                  </p>
                  <p className="mb-6">
                    What's your name?
                  </p>
                </div>
                
                {/* Name Form - only show if name not submitted */}
                {!nameSubmitted && (
                  <div className="mb-4">
                    <p className="text-white text-sm mb-3">My name is...</p>
                    <form onSubmit={handleSubmit} className="flex gap-3">
                      <input
                        type="text"
                        value={name}
                        onChange={(e) => setName(e.target.value)}
                        placeholder="Raymond"
                        className="flex-1 px-4 py-3 bg-white rounded-lg text-gray-900 placeholder-gray-500 border-none outline-none"
                        required
                        disabled={isSubmitting}
                      />
                      <button
                        type="submit"
                        disabled={isSubmitting || !name.trim()}
                        className="px-6 py-3 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isSubmitting ? 'Sending...' : 'Send'}
                      </button>
                    </form>
                  </div>
                )}
              </div>
            </div>

            {/* User Response - shown after name submission */}
            {nameSubmitted && (
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white font-medium text-sm">
                    {name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-white font-medium">{name}</span>
                  </div>
                  <div className="text-white text-base">
                    My name is {name}
                  </div>
                </div>
              </div>
            )}

            {/* AI Response after name submission */}
            {nameSubmitted && (
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                    <span className="text-black font-bold text-sm">V</span>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-white font-medium">Variant AI</span>
                  </div>
                  <div className="text-white text-base leading-relaxed">
                    <p className="mb-4">
                      Nice to meet you, {name}. I'll work on analyzing your website frontend now.
                    </p>
                    
                    {/* Analyzing Status */}
                    {isAnalyzing && !analysisComplete && (
                      <div className="flex items-center gap-3 bg-gray-700 px-4 py-2 rounded-full inline-flex">
                        <div className="w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"></div>
                        <span className="text-green-400 font-medium">Analyzing</span>
                      </div>
                    )}
                    
                    {/* Analysis Results */}
                    {analysisComplete && (
                      <div className="space-y-3">
                        <div className="flex items-center gap-3">
                          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-white">Detected: Webflow</span>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="w-5 h-5 bg-green-500 rounded-full flex items-center justify-center">
                            <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <span className="text-white">Detected: Google Analytics</span>
                        </div>
                        <p className="text-white mt-4">
                          Good news! I can integrate with your website.
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Traffic Information Request */}
            {showTrafficForm && (
              <div className="flex items-start gap-4">
                <div className="w-12 h-12 bg-gray-600 rounded-full flex items-center justify-center flex-shrink-0">
                  <div className="w-8 h-8 bg-white rounded-full flex items-center justify-center">
                    <span className="text-black font-bold text-sm">V</span>
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-3">
                    <span className="text-white font-medium">Variant AI</span>
                  </div>
                  <div className="text-white text-base leading-relaxed mb-6">
                    <p>Roughly how much traffic do you have on your website?</p>
                  </div>
                  
                  <form onSubmit={handleTrafficSubmit} className="space-y-4">
                    <div>
                      <label className="block text-white text-sm mb-2">Monthly visitors</label>
                      <input
                        type="text"
                        value={monthlyVisitors}
                        onChange={(e) => setMonthlyVisitors(e.target.value)}
                        placeholder="10,000"
                        className="w-full px-4 py-3 bg-white rounded-lg text-gray-900 placeholder-gray-500 border-none outline-none"
                        required
                        disabled={isSubmittingTraffic}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-white text-sm mb-2">Conversion rate</label>
                      <input
                        type="text"
                        value={conversionRate}
                        onChange={(e) => setConversionRate(e.target.value)}
                        placeholder="2.5%"
                        className="w-full px-4 py-3 bg-white rounded-lg text-gray-900 placeholder-gray-500 border-none outline-none"
                        required
                        disabled={isSubmittingTraffic}
                      />
                    </div>
                    
                    <div>
                      <label className="block text-white text-sm mb-2">Conversions</label>
                      <input
                        type="text"
                        value={conversions}
                        onChange={(e) => setConversions(e.target.value)}
                        placeholder="250"
                        className="w-full px-4 py-3 bg-white rounded-lg text-gray-900 placeholder-gray-500 border-none outline-none"
                        required
                        disabled={isSubmittingTraffic}
                      />
                    </div>
                    
                    <button
                      type="submit"
                      disabled={isSubmittingTraffic || !monthlyVisitors.trim() || !conversionRate.trim() || !conversions.trim()}
                      className="w-full py-3 bg-black text-white rounded-lg font-medium hover:bg-gray-800 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isSubmittingTraffic ? 'Processing...' : 'Continue'}
                    </button>
                  </form>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Onboarding; 
