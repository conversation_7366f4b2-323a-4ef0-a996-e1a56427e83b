import React from 'react';
import { Navigation } from '@/components/Navigation';
import { HeroSection } from '@/components/HeroSection';
import { ABTestingDemo } from '@/components/ABTestingDemo';
import { FeaturesSection } from '@/components/FeaturesSection';
import { CustomerStory } from '@/components/CustomerStory';
import { CTASection } from '@/components/CTASection';
import { Footer } from '@/components/Footer';

const Index = () => {
  return (
    <div className="w-full min-h-screen relative bg-[#0E0E0F] mx-auto">
      {/* Navigation */}
      <Navigation />
      
      <main>
        {/* Hero Section */}
        <section className="pt-[40px] pb-[40px] max-sm:pt-[30px] max-sm:pb-[30px]">
          <HeroSection />
        </section>

        {/* A/B Testing Demo */}
        <section className="pb-[120px] max-sm:pb-[80px]">
          <ABTestingDemo />
        </section>

        {/* Features Section */}
        <section className="py-[60px] max-sm:py-[30px]">
          <FeaturesSection />
        </section>

        {/* Customer Story */}
        <section className="py-[80px] max-sm:py-[40px]">
          <CustomerStory />
        </section>

        {/* Final CTA */}
        <section className="pt-[160px] pb-[300px] max-sm:pt-[80px] max-sm:pb-[160px]">
          <CTASection />
        </section>
      </main>

      {/* Footer */}
      <Footer />
    </div>
  );
};

export default Index;
