import React, { useState } from 'react';

const Signup = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email.trim() || !password.trim()) return;
    
    setIsSubmitting(true);
    
    // Handle signup logic here
    console.log('Signup attempt:', { email, password });
    
    // Simulate API call
    setTimeout(() => {
      setIsSubmitting(false);
      // Redirect to onboarding after successful signup
      window.location.href = '/onboarding';
    }, 1000);
  };

  return (
    <div className="min-h-screen bg-[#2A2A2A] flex items-center justify-center px-4">
      <div className="w-full max-w-md">
        {/* Variant AI Logo */}
        <div className="flex flex-col items-center mb-8">
          <div className="mb-4">
            <img 
              src="/favicon.ico" 
              alt="Variant AI" 
              className="w-12 h-12"
            />
          </div>
          <h1 className="text-white text-2xl font-semibold mb-2">Variant AI</h1>
          <p className="text-gray-400 text-center">Create an account to start working with Variant AI.</p>
        </div>

        {/* Signup Form */}
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Your email"
              className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={isSubmitting}
            />
          </div>
          
          <div>
            <input
              type="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
              className="w-full px-4 py-3 bg-white border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
              disabled={isSubmitting}
            />
          </div>

          <button
            type="submit"
            disabled={isSubmitting || !email.trim() || !password.trim()}
            className="w-full py-3 bg-black text-white rounded-lg font-medium hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            {isSubmitting ? 'Creating account...' : 'Continue'}
          </button>
        </form>

        {/* Terms and Privacy */}
        <p className="mt-6 text-sm text-gray-500 text-center">
          By signing up, you also agree to our{' '}
          <a href="#" className="text-blue-400 hover:text-blue-300">
            Terms of Use
          </a>{' '}
          and{' '}
          <a href="#" className="text-blue-400 hover:text-blue-300">
            Privacy Policy
          </a>
          .
        </p>
      </div>
    </div>
  );
};

export default Signup; 
