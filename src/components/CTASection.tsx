import React from 'react';
import { EmailForm } from './EmailForm';

export const CTASection: React.FC = () => {
  const handleUrlSubmit = (url: string) => {
    // Handle URL submission - store and redirect to signup
    console.log('CTA URL submitted:', url);
    // Store the URL for later use
    localStorage.setItem('submittedUrl', url);
    // Redirect to signup page
    window.location.href = '/signup';
  };

  return (
    <section className="flex w-[600px] flex-col items-center gap-8 mx-auto max-md:w-[90%] max-sm:w-[95%]">
      <h2 className="self-stretch text-white text-center text-5xl font-medium leading-[56px] tracking-[-0.96px] gap-2 max-md:text-[40px] max-md:leading-[48px] max-sm:text-[32px] max-sm:leading-10">
        Get started with the<br />
        #1 AI Agent for AB testing
      </h2>
      
      <div className="w-[480px] max-md:w-full">
        <EmailForm 
          onSubmit={handleUrlSubmit}
          className="w-full px-5 py-4 max-sm:w-full"
        />
      </div>
    </section>
  );
};
