import React from 'react';
import { useIsMobile } from '@/hooks/use-mobile';

export const ABTestingDemo: React.FC = () => {
  const isMobile = useIsMobile();

  return (
    <section className="flex gap-6 w-[1000px] mx-auto max-md:w-[90%] max-md:gap-4 max-sm:w-[95%] max-sm:gap-3">
      {/* Version A */}
      <article className="flex flex-col flex-1">
        <div className="w-[500px] h-[400px] max-w-full max-md:w-full max-md:h-[320px] max-sm:w-full max-sm:h-[280px] rounded-xl overflow-hidden bg-white shadow-lg">
          <img 
            src={isMobile ? "/images/variant_a_mobile.png" : "/images/variant_a.png"}
            alt="Version A website screenshot" 
            className="w-full h-full object-contain" 
          />
        </div>
        
        <div className="mt-4 text-center">
          <h3 className="text-white text-sm font-medium leading-5 max-sm:text-xs">A - 50% traffic</h3>
          <p className="text-white text-xs font-normal leading-5 tracking-[-0.24px] opacity-70 max-sm:text-[11px]">
            56.5% chance of winning
          </p>
        </div>
      </article>

      {/* Version B */}
      <article className="flex flex-col flex-1">
        <div className="w-[500px] h-[400px] max-w-full max-md:w-full max-md:h-[320px] max-sm:w-full max-sm:h-[280px] rounded-xl overflow-hidden bg-white shadow-lg">
          <img 
            src={isMobile ? "/images/variant_b_mobile.png" : "/gifs/variant_b.gif"}
            alt="Version B website screenshot" 
            className="w-full h-full object-contain" 
            loading="lazy"
            decoding="async"
            style={{ imageRendering: 'auto' }}
          />
        </div>
        
        <div className="mt-4 text-center">
          <h3 className="text-white text-sm font-medium leading-5 max-sm:text-xs">B - 50% traffic</h3>
          <p className="text-white text-xs font-normal leading-5 tracking-[-0.24px] opacity-70 max-sm:text-[11px]">
            86.2% chance of winning
          </p>
        </div>
      </article>
    </section>
  );
};
