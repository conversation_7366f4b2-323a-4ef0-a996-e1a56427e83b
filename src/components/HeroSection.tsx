import React from 'react';
import { EmailForm } from './EmailForm';

export const HeroSection: React.FC = () => {
  const handleUrlSubmit = (url: string) => {
    // Handle URL submission - store and redirect to signup
    console.log('Hero URL submitted:', url);
    // Store the URL for later use
    localStorage.setItem('submittedUrl', url);
    // Redirect to signup page
    window.location.href = '/signup';
  };

  return (
    <section className="flex w-[520px] flex-col items-center gap-8 mx-auto max-lg:w-[480px] max-md:w-[90%] max-sm:w-[95%] max-sm:gap-6">
      <div className="flex flex-col items-center gap-4 self-stretch max-sm:gap-3">
        <h1 className="self-stretch text-white text-center text-[52px] font-medium leading-[56px] tracking-[-1.04px] max-lg:text-[48px] max-lg:leading-[52px] max-md:text-[44px] max-md:leading-[48px] max-sm:text-[36px] max-sm:leading-[40px] max-sm:tracking-[-0.72px] max-xs:text-[32px] max-xs:leading-[36px]">
          The AI agent for<br />
          A/B testing
        </h1>
        <p className="self-stretch text-white text-center text-lg font-normal leading-7 tracking-[-0.36px] opacity-70 max-md:text-base max-md:leading-6 max-sm:text-[15px] max-sm:leading-[22px] max-sm:tracking-[-0.3px] max-xs:text-sm">
          Variant is an AI agent that runs, tests, and learns<br className="max-sm:hidden" />
          <span className="sm:hidden"> </span>from experiments.
        </p>
      </div>
      
      <div className="w-[440px] max-lg:w-[400px] max-md:w-full max-sm:w-full">
        <EmailForm 
          onSubmit={handleUrlSubmit}
          className="w-full"
        />
      </div>
    </section>
  );
};
