import React from 'react';

export const Navigation: React.FC = () => {
  return (
    <header className="flex w-full justify-between items-center h-[69px] px-6 py-4 max-md:px-5 max-sm:px-4 max-sm:py-3 max-sm:h-[60px]">
      <div className="flex items-center gap-2 max-sm:gap-1.5">
        {/* V Logo */}
        <svg width="32" height="31" viewBox="0 0 32 31" fill="none" xmlns="http://www.w3.org/2000/svg" className="max-sm:w-7 max-sm:h-7">
          <path d="M1.84833 4.52656C1.5419 3.92552 2.01676 3.23126 2.68614 3.31538C4.71543 3.57038 8.22786 4.1144 9.97934 4.98964C12.5301 6.2643 13.4854 8.13049 13.6578 10.481L14.9248 27.7572C14.9704 28.379 14.129 28.614 13.8458 28.0585L1.84833 4.52656Z" fill="white"/>
          <path d="M29.885 4.78879C30.1861 4.19209 29.7206 3.50237 29.056 3.57368C27.0177 3.79238 23.4805 4.26003 22.0206 4.98958C19.912 6.04331 18.5146 8.13042 18.3421 10.481L17.0774 27.727C17.0317 28.3501 17.8759 28.584 18.1574 28.0262L29.885 4.78879Z" fill="white"/>
        </svg>
        
        {/* VariantAI Text */}
        <span className="text-white text-xl font-medium max-sm:text-lg">VariantAI</span>
      </div>
      
      <button className="bg-white text-black text-center text-sm font-medium leading-[19px] tracking-[-0.28px] px-[18px] py-2 rounded-[100px] hover:bg-gray-100 transition-colors max-sm:px-4 max-sm:py-1.5 max-sm:text-[13px] max-sm:leading-[17px]">
        Get Started
      </button>
    </header>
  );
};
