import React from 'react';

export const BrowserChrome: React.FC = () => {
  return (
    <div className="flex w-full h-[79px] justify-center items-center shrink-0 max-sm:hidden">
      <div className="w-full h-[79px] shrink-0">
        {/* Tabs Bar */}
        <div className="w-full h-[42px] shrink-0 relative">
          <div className="w-full h-[42px] shrink-0 absolute bg-[#202124] left-0 top-0" />
          
          {/* Traffic Lights */}
          <div className="absolute left-[13px] top-4">
            <svg width="52" height="12" viewBox="0 0 52 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              <g filter="url(#filter0_i_1_1603)">
                <path fillRule="evenodd" clipRule="evenodd" d="M6 12C9.31371 12 12 9.31371 12 6C12 2.68629 9.31371 0 6 0C2.68629 0 0 2.68629 0 6C0 9.31371 2.68629 12 6 12Z" fill="#ED6A5E"/>
              </g>
              <path d="M6 0.25C9.17564 0.25 11.75 2.82436 11.75 6C11.75 9.17564 9.17564 11.75 6 11.75C2.82436 11.75 0.25 9.17564 0.25 6C0.25 2.82436 2.82436 0.25 6 0.25Z" stroke="#CE5347" strokeWidth="0.5"/>
              <g filter="url(#filter1_i_1_1603)">
                <path fillRule="evenodd" clipRule="evenodd" d="M26 12C29.3137 12 32 9.31371 32 6C32 2.68629 29.3137 0 26 0C22.6863 0 20 2.68629 20 6C20 9.31371 22.6863 12 26 12Z" fill="#F6BE4F"/>
              </g>
              <path d="M26 0.25C29.1756 0.25 31.75 2.82436 31.75 6C31.75 9.17564 29.1756 11.75 26 11.75C22.8244 11.75 20.25 9.17564 20.25 6C20.25 2.82436 22.8244 0.25 26 0.25Z" stroke="#D6A243" strokeWidth="0.5"/>
              <g filter="url(#filter2_i_1_1603)">
                <path fillRule="evenodd" clipRule="evenodd" d="M46 12C49.3137 12 52 9.31371 52 6C52 2.68629 49.3137 0 46 0C42.6863 0 40 2.68629 40 6C40 9.31371 42.6863 12 46 12Z" fill="#62C554"/>
              </g>
              <path d="M46 0.25C49.1756 0.25 51.75 2.82436 51.75 6C51.75 9.17564 49.1756 11.75 46 11.75C42.8244 11.75 40.25 9.17564 40.25 6C40.25 2.82436 42.8244 0.25 46 0.25Z" stroke="#58A942" strokeWidth="0.5"/>
              <defs>
                <filter id="filter0_i_1_1603" x="0" y="0" width="12" height="12" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                  <feOffset/>
                  <feGaussianBlur stdDeviation="3"/>
                  <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                  <feColorMatrix type="matrix" values="0 0 0 0 0.92549 0 0 0 0 0.427451 0 0 0 0 0.384314 0 0 0 1 0"/>
                  <feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_1603"/>
                </filter>
                <filter id="filter1_i_1_1603" x="20" y="0" width="12" height="12" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                  <feOffset/>
                  <feGaussianBlur stdDeviation="3"/>
                  <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                  <feColorMatrix type="matrix" values="0 0 0 0 0.960784 0 0 0 0 0.768627 0 0 0 0 0.317647 0 0 0 1 0"/>
                  <feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_1603"/>
                </filter>
                <filter id="filter2_i_1_1603" x="40" y="0" width="12" height="12" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                  <feFlood floodOpacity="0" result="BackgroundImageFix"/>
                  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
                  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                  <feOffset/>
                  <feGaussianBlur stdDeviation="3"/>
                  <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
                  <feColorMatrix type="matrix" values="0 0 0 0 0.407843 0 0 0 0 0.8 0 0 0 0 0.345098 0 0 0 1 0"/>
                  <feBlend mode="normal" in2="shape" result="effect1_innerShadow_1_1603"/>
                </filter>
              </defs>
            </svg>
          </div>

          {/* Tab */}
          <div className="w-64 h-[34px] shrink-0 absolute left-[70px] top-2">
            <svg width="256" height="34" viewBox="0 0 256 34" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M240 0C244.418 3.60783e-06 248 3.58172 248 8V26C248 30.4183 251.582 34 256 34H0C4.41828 34 8 30.4183 8 26V8C8 3.58172 11.5817 2.2549e-07 16 0H240Z" fill="#35363A"/>
            </svg>
            <img src="https://cdn.builder.io/api/v1/image/assets/TEMP/abc0e2d5ddc4af96caa4a3c8b756bb7a12975a35?width=36" alt="" className="w-[18px] h-4 shrink-0 absolute left-[19px] top-[9px]" />
            <div className="w-[177px] h-5 shrink-0 absolute left-11 top-[7px]">
              <div className="w-[239px] h-5 shrink-0 text-white text-xs font-normal absolute left-[21px] top-0">Figma</div>
            </div>
            <svg className="w-2 h-2 shrink-0 absolute left-[228px] top-[13px]" width="8" height="9" viewBox="0 0 8 9" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M0.281234 6.81562C0.00509165 7.09177 0.00509153 7.53948 0.281234 7.81562C0.557376 8.09177 1.00509 8.09177 1.28123 7.81562L4.03905 5.05781L6.79686 7.81562C7.073 8.09177 7.52072 8.09177 7.79686 7.81562C8.073 7.53948 8.073 7.09177 7.79686 6.81562L5.03905 4.05781L7.79686 1.3C8.073 1.02386 8.073 0.576141 7.79686 0.299998C7.52072 0.023856 7.073 0.0238561 6.79686 0.299999L4.03905 3.05781L1.28123 0.299999C1.00509 0.0238561 0.557376 0.023856 0.281234 0.299998C0.00509153 0.576141 0.00509165 1.02386 0.281234 1.3L3.03905 4.05781L0.281234 6.81562Z" fill="white"/>
            </svg>
          </div>

          {/* New Tab Icon */}
          <svg className="absolute left-[334px] top-[19px]" width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6 0C5.44772 0 5 0.447715 5 1V5H1C0.447715 5 0 5.44772 0 6C0 6.55228 0.447715 7 1 7H5V11C5 11.5523 5.44772 12 6 12C6.55228 12 7 11.5523 7 11V7H11C11.5523 7 12 6.55228 12 6C12 5.44772 11.5523 5 11 5H7V1C7 0.447715 6.55228 0 6 0Z" fill="#BEC1C5"/>
          </svg>
        </div>

        {/* Toolbar */}
        <div className="w-full h-[37px] shrink-0 relative">
          <div className="w-full h-[37px] shrink-0 absolute bg-[#35363A] left-0 top-0" />
          <div className="w-full h-px shrink-0 absolute bg-[#282828] left-0 top-[37px]" />
          
          {/* Navigation Actions */}
          <svg className="absolute left-4 top-3" width="78" height="14" viewBox="0 0 78 14" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M7.28487 0.817998C7.57523 1.1134 7.57115 1.58825 7.27575 1.87862L3.0829 6H12.75C13.1642 6 13.5 6.33579 13.5 6.75C13.5 7.16421 13.1642 7.5 12.75 7.5H3.09051L7.2742 11.5886C7.57044 11.8781 7.5759 12.353 7.28639 12.6492C6.99688 12.9454 6.52204 12.9509 6.2258 12.6614L0.7258 7.28639C0.581673 7.14554 0.500293 6.95261 0.500001 6.75109C0.499709 6.54956 0.580529 6.3564 0.724248 6.21513L6.22425 0.808881C6.51965 0.518516 6.9945 0.522598 7.28487 0.817998Z" fill="#F1F3F4"/>
            <path fillRule="evenodd" clipRule="evenodd" d="M38.7151 0.817998C38.4248 1.1134 38.4288 1.58825 38.7242 1.87862L42.9171 6H33.25C32.8358 6 32.5 6.33579 32.5 6.75C32.5 7.16421 32.8358 7.5 33.25 7.5H42.9095L38.7258 11.5886C38.4296 11.8781 38.4241 12.353 38.7136 12.6492C39.0031 12.9454 39.478 12.9509 39.7742 12.6614L45.2742 7.28639C45.4183 7.14554 45.4997 6.95261 45.5 6.75109C45.5003 6.54956 45.4195 6.3564 45.2758 6.21513L39.7758 0.808881C39.4804 0.518516 39.0055 0.522598 38.7151 0.817998Z" fill="#86888A"/>
            <path d="M66 7C66 4.23858 68.2386 2 71 2C72.5073 2 73.8594 2.6667 74.7768 3.7232L72.5 6H78V0.5L75.8394 2.66063C74.65 1.33512 72.9228 0.5 71 0.5C67.4101 0.5 64.5 3.41015 64.5 7C64.5 10.5899 67.4101 13.5 71 13.5C73.5781 13.5 75.8042 11.999 76.8547 9.8265C77.0351 9.45359 76.8789 9.00512 76.506 8.8248C76.1331 8.64448 75.6847 8.8006 75.5043 9.1735C74.6947 10.8478 72.9812 12 71 12C68.2386 12 66 9.76142 66 7Z" fill="#F1F3F4"/>
          </svg>

          {/* Address Bar */}
          <div className="absolute left-[108px] top-1 right-4 h-7">
            <div className="w-full h-7 bg-[#202124] rounded-[14px]" />
            <svg className="absolute left-[14px] top-[9px]" width="8" height="11" viewBox="0 0 8 11" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path fillRule="evenodd" clipRule="evenodd" d="M4 0C2.61929 0 1.5 1.11929 1.5 2.5V3.5H1C0.447715 3.5 0 3.94772 0 4.5V9.5C0 10.0523 0.447715 10.5 1 10.5H7C7.55228 10.5 8 10.0523 8 9.5V4.5C8 3.94772 7.55228 3.5 7 3.5H6.5V2.5C6.5 1.11929 5.38071 0 4 0ZM5.5 3.5V2.5C5.5 1.67157 4.82843 1 4 1C3.17157 1 2.5 1.67157 2.5 2.5V3.5H5.5Z" fill="#EAEAEA"/>
            </svg>
            <div className="absolute left-[54px] top-0 text-white text-sm font-normal h-7 flex items-center">V.com</div>
            <svg className="absolute right-[23px] top-3" width="4" height="14" viewBox="0 0 4 14" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M3.5 2.09998C3.5 2.9284 2.82843 3.59998 2 3.59998C1.17157 3.59998 0.5 2.9284 0.5 2.09998C0.5 1.27155 1.17157 0.599976 2 0.599976C2.82843 0.599976 3.5 1.27155 3.5 2.09998Z" fill="#F1F3F4"/>
              <path d="M3.5 7.09998C3.5 7.9284 2.82843 8.59998 2 8.59998C1.17157 8.59998 0.5 7.9284 0.5 7.09998C0.5 6.27155 1.17157 5.59998 2 5.59998C2.82843 5.59998 3.5 6.27155 3.5 7.09998Z" fill="#F1F3F4"/>
              <path d="M3.5 12.1C3.5 12.9284 2.82843 13.6 2 13.6C1.17157 13.6 0.5 12.9284 0.5 12.1C0.5 11.2715 1.17157 10.6 2 10.6C2.82843 10.6 3.5 11.2715 3.5 12.1Z" fill="#F1F3F4"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};
