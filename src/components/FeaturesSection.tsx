import React from 'react';

// Alternative approach using video for better performance
const OptimizedMedia: React.FC<{ 
  gifSrc: string; 
  videoSrc?: string; 
  alt: string; 
  className?: string; 
}> = ({ gifSrc, videoSrc, alt, className }) => {
  // Use video if available, fallback to GIF
  if (videoSrc) {
    return (
      <video
        autoPlay
        loop
        muted
        playsInline
        className={className}
        poster={gifSrc.replace('.gif', '.jpg')} // Optional: add poster image
      >
        <source src={videoSrc} type="video/mp4" />
        {/* Fallback to GIF */}
        <img src={gifSrc} alt={alt} className={className} />
      </video>
    );
  }
  
  return (
    <img 
      src={gifSrc} 
      alt={alt} 
      className={className}
      loading="lazy"
      decoding="async"
      style={{ imageRendering: 'auto' }}
    />
  );
};

export const FeaturesSection: React.FC = () => {
  return (
    <section className="w-[846px] mx-auto max-md:w-[90%] max-sm:w-[95%]">
      {/* Section Header */}
      <div className="flex w-[620px] flex-col items-center gap-6 mx-auto mb-8 max-md:w-full">
        <h2 className="text-white text-center text-5xl font-medium leading-[56px] tracking-[-0.48px] max-md:text-[40px] max-md:leading-[48px] max-sm:text-[32px] max-sm:leading-10">
          Meet your new<br />
          AI teammate
        </h2>
        <p className="text-[rgba(255,255,255,0.70)] text-center text-lg font-normal leading-[30px] tracking-[-0.36px] max-sm:text-base max-sm:leading-6">
          Variant operates end-to-end, handling the entire A/B testing lifecycle without manual intervention.
        </p>
      </div>

      {/* Features */}
      <div className="flex flex-col gap-8">
        {/* Ideation */}
        <article className="inline-flex items-center gap-[66px] w-full max-md:flex-col max-md:gap-6">
          <div className="w-[300px] h-[200px] relative max-md:w-full max-md:max-w-[400px] max-sm:max-w-[300px] max-sm:mx-auto">
            <OptimizedMedia
              gifSrc="/gifs/1.gif"
              // videoSrc="/videos/1.mp4" // Uncomment when you have MP4 versions
              alt="Ideation process mockup"
              className="w-full h-full object-cover rounded border border-solid border-[#505066]"
            />
          </div>
          
          <div className="flex w-[480px] flex-col items-center gap-6 max-md:w-full">
            <h3 className="self-stretch text-white text-[28px] font-medium leading-10 max-sm:text-2xl max-sm:leading-8 max-sm:text-center">
              Ideation
            </h3>
            <p className="self-stretch text-[rgba(255,255,255,0.70)] text-base font-normal leading-[26px] tracking-[-0.32px] max-sm:text-sm max-sm:leading-[22px] max-sm:text-center">
              Variant thinks like a growth PM, proposing high-impact experiments to run on headlines, layouts, and images.
            </p>
          </div>
        </article>

        {/* Implementation */}
        <article className="inline-flex items-center gap-[66px] w-full max-md:flex-col max-md:gap-6">
          <div className="w-[300px] h-[200px] relative max-md:w-full max-md:max-w-[400px] max-sm:max-w-[300px] max-sm:mx-auto">
            <OptimizedMedia
              gifSrc="/gifs/2.gif"
              // videoSrc="/videos/2.mp4" // Uncomment when you have MP4 versions
              alt="Implementation process mockup"
              className="w-full h-full object-cover rounded bg-[#F8F9FA]"
            />
          </div>
          
          <div className="flex w-[480px] flex-col items-center gap-6 max-md:w-full">
            <h3 className="self-stretch text-white text-[28px] font-medium leading-10 max-sm:text-2xl max-sm:leading-8 max-sm:text-center">
              Implementation
            </h3>
            <p className="self-stretch text-[rgba(255,255,255,0.70)] text-base font-normal leading-[26px] tracking-[-0.32px] max-sm:text-sm max-sm:leading-[22px] max-sm:text-center">
              Variant writes copy, builds variants, and ships — all without involving your dev team.
            </p>
          </div>
        </article>

        {/* Analysis */}
        <article className="inline-flex items-center gap-[66px] w-full max-md:flex-col max-md:gap-6">
          <div className="w-[300px] h-[200px] relative max-md:w-full max-md:max-w-[400px] max-sm:max-w-[300px] max-sm:mx-auto">
            <OptimizedMedia
              gifSrc="/gifs/3.gif"
              // videoSrc="/videos/3.mp4" // Uncomment when you have MP4 versions
              alt="Analysis process mockup"
              className="w-full h-full object-cover rounded bg-[#F8F9FA]"
            />
          </div>
          
          <div className="flex w-[480px] flex-col items-center gap-6 max-md:w-full">
            <h3 className="self-stretch text-white text-[28px] font-medium leading-10 max-sm:text-2xl max-sm:leading-8 max-sm:text-center">
              Analysis
            </h3>
            <p className="self-stretch text-[rgba(255,255,255,0.70)] text-base font-normal leading-[26px] tracking-[-0.32px] max-sm:text-sm max-sm:leading-[22px] max-sm:text-center">
              Variant monitors performance, promotes winners, and learns what works to improve future tests.
            </p>
          </div>
        </article>
      </div>
    </section>
  );
};
