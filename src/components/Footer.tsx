import React, { useState } from 'react';
import { Plus } from 'lucide-react';

interface FAQItem {
  question: string;
  answer: string;
}

const faqItems: FAQItem[] = [
  {
    question: "What is A/B testing?",
    answer: "A/B testing helps you improve your website's conversion rate by showing different versions of a page to real visitors and measuring which one performs better. It's a reliable, data-driven way to optimize for more signups, purchases, or engagement—without guessing."
  },
  {
    question: "How does Variant AI automate my tests?",
    answer: "Variant AI handles the entire A/B testing process for you. It generates test ideas, makes the page changes (copy, layout, or design), launches the variants, routes traffic, analyzes results, and automatically ships the winning version. No manual setup or ongoing management needed."
  },
  {
    question: "What kind of results can I realistically expect?",
    answer: "Most teams see a 5–15% lift in conversions or revenue in the first month, depending on your site's traffic and baseline performance. Variant continuously tests and improves, so gains compound over time as it learns what works."
  },
  {
    question: "Will Variant AI work on my website?",
    answer: "Yes. Variant works with nearly any website. You can install it via a lightweight JavaScript snippet, or use plugins for Webflow, Shopify, and WordPress. It also supports custom stacks like React, Next.js, and static sites."
  },
  {
    question: "Do I need an engineer to set this up?",
    answer: "No. Setup takes about 10 minutes and doesn't require any coding if you use a supported platform. Just paste the snippet and go. Developers are only needed for advanced integrations or custom workflows."
  }
];

export const Footer: React.FC = () => {
  const [openFAQ, setOpenFAQ] = useState<number | null>(null);

  const toggleFAQ = (index: number) => {
    setOpenFAQ(openFAQ === index ? null : index);
  };

  return (
    <footer className="w-full bg-[#181819]">
      <div className="max-w-[1300px] mx-auto px-4 py-[73px] max-md:px-6">
        {/* Desktop layout */}
        <div className="hidden md:flex justify-between items-start gap-12">
          {/* Left side - Company info */}
          <div className="flex flex-col items-start gap-7 w-[350px] shrink-0">
            <div className="flex items-center gap-[7.606px]">
              <svg width="117" height="31" viewBox="0 0 117 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.84834 4.52656C1.54191 3.92552 2.01677 3.23126 2.68614 3.31538C4.71544 3.57038 8.22787 4.1144 9.97935 4.98964C12.5301 6.2643 13.4854 8.13049 13.6578 10.481L14.9248 27.7572C14.9704 28.379 14.129 28.614 13.8458 28.0585L1.84834 4.52656Z" fill="white"/>
                <path d="M29.885 4.78879C30.1861 4.19209 29.7205 3.50237 29.056 3.57368C27.0177 3.79238 23.4805 4.26003 22.0206 4.98958C19.912 6.04331 18.5146 8.13042 18.3421 10.481L17.0774 27.727C17.0317 28.3501 17.8759 28.584 18.1574 28.0262L29.885 4.78879Z" fill="white"/>
                <path d="M116.051 8.88257V21.6986H114.017V8.88257H116.051Z" fill="white"/>
                <path d="M110.71 21.6986L109.612 18.7646H104.212L103.114 21.6986H101.044L105.958 8.88257H108.01L112.906 21.6986H110.71ZM104.842 17.1266H109L106.93 11.5646L104.842 17.1266Z" fill="white"/>
                <path d="M95.8467 21.6986C94.2447 21.6986 93.3627 20.7986 93.3627 19.1966V14.2286H91.8147V12.4826H92.6067C93.1827 12.4826 93.3627 12.1946 93.3627 11.6186V10.0166H95.3247V12.4826H97.4667V14.2286H95.3247V18.8186C95.3247 19.5566 95.5407 19.9526 96.4047 19.9526H97.4487V21.6986H95.8467Z" fill="white"/>
                <path d="M87.2669 12.3025C89.5889 12.3025 90.9569 13.8325 90.9569 16.1905V21.6985H88.9769V16.6045C88.9769 14.8765 88.0949 14.0305 86.7449 14.0305C85.2149 14.0305 84.3869 15.1105 84.3869 16.7485V21.6985H82.4249V12.4825H84.2069L84.3509 13.7245C84.8729 12.8425 85.8629 12.3025 87.2669 12.3025Z" fill="white"/>
                <path d="M80.783 20.2225H81.125V21.6985H80.009C79.253 21.6985 78.569 21.4105 78.425 20.5285C77.903 21.3385 76.769 21.8785 75.329 21.8785C73.295 21.8785 71.999 20.8165 71.999 19.0525C71.999 17.4865 72.989 16.3345 75.509 16.3345H78.191V15.8665C78.191 14.5165 77.381 13.8865 76.175 13.8865C74.951 13.8865 74.159 14.4985 74.105 15.4525H72.287C72.341 13.4545 73.979 12.3025 76.247 12.3025C78.407 12.3025 80.171 13.3825 80.171 15.8845V19.6105C80.171 20.1145 80.369 20.2225 80.783 20.2225ZM78.191 18.0625V17.6845H75.779C74.447 17.6845 74.033 18.2785 74.033 18.9625C74.033 19.8985 74.771 20.3125 75.779 20.3125C77.147 20.3125 78.191 19.5745 78.191 18.0625Z" fill="white"/>
                <path d="M68.3767 10.7546V8.70264H70.5547V10.7546H68.3767ZM68.4847 21.6986V12.4826H70.4467V21.6986H68.4847Z" fill="white"/>
                <path d="M66.0787 12.4827H67.2847V14.2467H65.9707C64.4407 14.2467 63.8467 15.2187 63.8467 16.7847V21.6987H61.8667V12.4827H63.6667L63.9007 13.6707C64.2427 13.0227 65.0167 12.4827 66.0787 12.4827Z" fill="white"/>
                <path d="M60.2067 20.2225H60.5487V21.6985H59.4327C58.6767 21.6985 57.9927 21.4105 57.8487 20.5285C57.3267 21.3385 56.1927 21.8785 54.7527 21.8785C52.7187 21.8785 51.4227 20.8165 51.4227 19.0525C51.4227 17.4865 52.4127 16.3345 54.9327 16.3345H57.6147V15.8665C57.6147 14.5165 56.8047 13.8865 55.5987 13.8865C54.3747 13.8865 53.5827 14.4985 53.5287 15.4525H51.7107C51.7647 13.4545 53.4027 12.3025 55.6707 12.3025C57.8307 12.3025 59.5947 13.3825 59.5947 15.8845V19.6105C59.5947 20.1145 59.7927 20.2225 60.2067 20.2225ZM57.6147 18.0625V17.6845H55.2027C53.8707 17.6845 53.4567 18.2785 53.4567 18.9625C53.4567 19.8985 54.1947 20.3125 55.2027 20.3125C56.5707 20.3125 57.6147 19.5745 57.6147 18.0625Z" fill="white"/>
                <path d="M45.7616 19.2686L49.6856 8.88257H51.8456L46.8056 21.6986H44.6276L39.6056 8.88257H41.8196L45.7616 19.2686Z" fill="white"/>
              </svg>
            </div>
            <p className="text-white text-sm font-normal leading-[26px] tracking-[-0.28px] opacity-50">
              Variant AI is an AI Agent for AB testing the handles all the busywork so you can focus on what matters.
            </p>
          </div>

          {/* Right side - FAQ */}
          <div className="flex flex-col items-start gap-5 flex-1 max-w-[620px]">
            <h3 className="text-white text-2xl font-medium leading-8 max-sm:text-xl max-sm:leading-7">
              FAQ
            </h3>
            
            <div className="flex flex-col items-start gap-0 w-full">
              {faqItems.map((item, index) => (
                <div key={index} className="w-full">
                  <div className="w-full h-px opacity-15 bg-white" />
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="flex justify-between items-center w-full py-4 text-left hover:opacity-80 transition-opacity"
                    aria-expanded={openFAQ === index}
                  >
                    <div className="text-white text-base font-normal leading-[26px] tracking-[-0.32px] opacity-70 pr-4 max-sm:text-sm max-sm:leading-[22px]">
                      {item.question}
                    </div>
                    <Plus 
                      className={`w-5 h-5 text-[#C4C4C4] flex-shrink-0 transition-transform ${
                        openFAQ === index ? 'rotate-45' : 'rotate-0'
                      }`}
                    />
                  </button>
                  {openFAQ === index && (
                    <div className="pb-4 text-white text-sm font-normal leading-[22px] tracking-[-0.28px] opacity-60">
                      {item.answer}
                    </div>
                  )}
                </div>
              ))}
              <div className="w-full h-px opacity-15 bg-white" />
            </div>
          </div>
        </div>

        {/* Mobile layout */}
        <div className="block md:hidden">
          {/* Company info section */}
          <div className="flex flex-col items-start gap-7 w-full mb-8">
            <div className="flex items-center gap-[7.606px]">
              <svg width="117" height="31" viewBox="0 0 117 31" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M1.84834 4.52656C1.54191 3.92552 2.01677 3.23126 2.68614 3.31538C4.71544 3.57038 8.22787 4.1144 9.97935 4.98964C12.5301 6.2643 13.4854 8.13049 13.6578 10.481L14.9248 27.7572C14.9704 28.379 14.129 28.614 13.8458 28.0585L1.84834 4.52656Z" fill="white"/>
                <path d="M29.885 4.78879C30.1861 4.19209 29.7205 3.50237 29.056 3.57368C27.0177 3.79238 23.4805 4.26003 22.0206 4.98958C19.912 6.04331 18.5146 8.13042 18.3421 10.481L17.0774 27.727C17.0317 28.3501 17.8759 28.584 18.1574 28.0262L29.885 4.78879Z" fill="white"/>
                <path d="M116.051 8.88257V21.6986H114.017V8.88257H116.051Z" fill="white"/>
                <path d="M110.71 21.6986L109.612 18.7646H104.212L103.114 21.6986H101.044L105.958 8.88257H108.01L112.906 21.6986H110.71ZM104.842 17.1266H109L106.93 11.5646L104.842 17.1266Z" fill="white"/>
                <path d="M95.8467 21.6986C94.2447 21.6986 93.3627 20.7986 93.3627 19.1966V14.2286H91.8147V12.4826H92.6067C93.1827 12.4826 93.3627 12.1946 93.3627 11.6186V10.0166H95.3247V12.4826H97.4667V14.2286H95.3247V18.8186C95.3247 19.5566 95.5407 19.9526 96.4047 19.9526H97.4487V21.6986H95.8467Z" fill="white"/>
                <path d="M87.2669 12.3025C89.5889 12.3025 90.9569 13.8325 90.9569 16.1905V21.6985H88.9769V16.6045C88.9769 14.8765 88.0949 14.0305 86.7449 14.0305C85.2149 14.0305 84.3869 15.1105 84.3869 16.7485V21.6985H82.4249V12.4825H84.2069L84.3509 13.7245C84.8729 12.8425 85.8629 12.3025 87.2669 12.3025Z" fill="white"/>
                <path d="M80.783 20.2225H81.125V21.6985H80.009C79.253 21.6985 78.569 21.4105 78.425 20.5285C77.903 21.3385 76.769 21.8785 75.329 21.8785C73.295 21.8785 71.999 20.8165 71.999 19.0525C71.999 17.4865 72.989 16.3345 75.509 16.3345H78.191V15.8665C78.191 14.5165 77.381 13.8865 76.175 13.8865C74.951 13.8865 74.159 14.4985 74.105 15.4525H72.287C72.341 13.4545 73.979 12.3025 76.247 12.3025C78.407 12.3025 80.171 13.3825 80.171 15.8845V19.6105C80.171 20.1145 80.369 20.2225 80.783 20.2225ZM78.191 18.0625V17.6845H75.779C74.447 17.6845 74.033 18.2785 74.033 18.9625C74.033 19.8985 74.771 20.3125 75.779 20.3125C77.147 20.3125 78.191 19.5745 78.191 18.0625Z" fill="white"/>
                <path d="M68.3767 10.7546V8.70264H70.5547V10.7546H68.3767ZM68.4847 21.6986V12.4826H70.4467V21.6986H68.4847Z" fill="white"/>
                <path d="M66.0787 12.4827H67.2847V14.2467H65.9707C64.4407 14.2467 63.8467 15.2187 63.8467 16.7847V21.6987H61.8667V12.4827H63.6667L63.9007 13.6707C64.2427 13.0227 65.0167 12.4827 66.0787 12.4827Z" fill="white"/>
                <path d="M60.2067 20.2225H60.5487V21.6985H59.4327C58.6767 21.6985 57.9927 21.4105 57.8487 20.5285C57.3267 21.3385 56.1927 21.8785 54.7527 21.8785C52.7187 21.8785 51.4227 20.8165 51.4227 19.0525C51.4227 17.4865 52.4127 16.3345 54.9327 16.3345H57.6147V15.8665C57.6147 14.5165 56.8047 13.8865 55.5987 13.8865C54.3747 13.8865 53.5827 14.4985 53.5287 15.4525H51.7107C51.7647 13.4545 53.4027 12.3025 55.6707 12.3025C57.8307 12.3025 59.5947 13.3825 59.5947 15.8845V19.6105C59.5947 20.1145 59.7927 20.2225 60.2067 20.2225ZM57.6147 18.0625V17.6845H55.2027C53.8707 17.6845 53.4567 18.2785 53.4567 18.9625C53.4567 19.8985 54.1947 20.3125 55.2027 20.3125C56.5707 20.3125 57.6147 19.5745 57.6147 18.0625Z" fill="white"/>
                <path d="M45.7616 19.2686L49.6856 8.88257H51.8456L46.8056 21.6986H44.6276L39.6056 8.88257H41.8196L45.7616 19.2686Z" fill="white"/>
              </svg>
            </div>
            <p className="text-white text-sm font-normal leading-[26px] tracking-[-0.28px] opacity-50">
              Variant AI is an AI Agent for AB testing the handles all the busywork so you can focus on what matters.
            </p>
          </div>

          {/* FAQ section - full width on mobile */}
          <div className="flex flex-col items-start gap-5 w-full">
            <h3 className="text-white text-2xl font-medium leading-8 max-sm:text-xl max-sm:leading-7">
              FAQ
            </h3>
            
            <div className="flex flex-col items-start gap-0 w-full">
              {faqItems.map((item, index) => (
                <div key={index} className="w-full">
                  <div className="w-full h-px opacity-15 bg-white" />
                  <button
                    onClick={() => toggleFAQ(index)}
                    className="flex justify-between items-center w-full py-4 text-left hover:opacity-80 transition-opacity"
                    aria-expanded={openFAQ === index}
                  >
                    <div className="text-white text-base font-normal leading-[26px] tracking-[-0.32px] opacity-70 pr-4 max-sm:text-sm max-sm:leading-[22px]">
                      {item.question}
                    </div>
                    <Plus 
                      className={`w-5 h-5 text-[#C4C4C4] flex-shrink-0 transition-transform ${
                        openFAQ === index ? 'rotate-45' : 'rotate-0'
                      }`}
                    />
                  </button>
                  {openFAQ === index && (
                    <div className="pb-4 text-white text-sm font-normal leading-[22px] tracking-[-0.28px] opacity-60">
                      {item.answer}
                    </div>
                  )}
                </div>
              ))}
              <div className="w-full h-px opacity-15 bg-white" />
            </div>
          </div>
        </div>
      </div>

      {/* Bottom section */}
      <div className="max-w-[1300px] mx-auto px-4 py-4 max-md:px-6">
        <div className="flex justify-between items-center gap-6 max-md:flex-col max-md:gap-8 max-md:items-start max-sm:gap-6 max-md:px-0">
          {/* Social links */}
          <div className="flex flex-col items-start gap-4">
            <div className="text-white text-xs font-medium leading-[14.4px]">Social</div>
            <div className="flex items-center gap-6 max-md:items-start">
              <a href="#" aria-label="Instagram" className="hover:opacity-80 transition-opacity">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clipPath="url(#clip0_1_1527)">
                    <path d="M10 1.80078C12.6719 1.80078 12.9883 1.8125 14.0391 1.85937C15.0156 1.90234 15.543 2.06641 15.8945 2.20313C16.3594 2.38281 16.6953 2.60156 17.043 2.94922C17.3945 3.30078 17.6094 3.63281 17.7891 4.09766C17.9258 4.44922 18.0898 4.98047 18.1328 5.95313C18.1797 7.00781 18.1914 7.32422 18.1914 9.99219C18.1914 12.6641 18.1797 12.9805 18.1328 14.0313C18.0898 15.0078 17.9258 15.5352 17.7891 15.8867C17.6094 16.3516 17.3906 16.6875 17.043 17.0352C16.6914 17.3867 16.3594 17.6016 15.8945 17.7813C15.543 17.918 15.0117 18.082 14.0391 18.125C12.9844 18.1719 12.668 18.1836 10 18.1836C7.32813 18.1836 7.01172 18.1719 5.96094 18.125C4.98438 18.082 4.45703 17.918 4.10547 17.7813C3.64063 17.6016 3.30469 17.3828 2.95703 17.0352C2.60547 16.6836 2.39063 16.3516 2.21094 15.8867C2.07422 15.5352 1.91016 15.0039 1.86719 14.0313C1.82031 12.9766 1.80859 12.6602 1.80859 9.99219C1.80859 7.32031 1.82031 7.00391 1.86719 5.95313C1.91016 4.97656 2.07422 4.44922 2.21094 4.09766C2.39063 3.63281 2.60938 3.29688 2.95703 2.94922C3.30859 2.59766 3.64063 2.38281 4.10547 2.20313C4.45703 2.06641 4.98828 1.90234 5.96094 1.85937C7.01172 1.8125 7.32813 1.80078 10 1.80078ZM10 0C7.28516 0 6.94531 0.0117187 5.87891 0.0585938C4.81641 0.105469 4.08594 0.277344 3.45313 0.523437C2.79297 0.78125 2.23438 1.12109 1.67969 1.67969C1.12109 2.23438 0.78125 2.79297 0.523438 3.44922C0.277344 4.08594 0.105469 4.8125 0.0585938 5.875C0.0117188 6.94531 0 7.28516 0 10C0 12.7148 0.0117188 13.0547 0.0585938 14.1211C0.105469 15.1836 0.277344 15.9141 0.523438 16.5469C0.78125 17.207 1.12109 17.7656 1.67969 18.3203C2.23438 18.875 2.79297 19.2188 3.44922 19.4727C4.08594 19.7188 4.8125 19.8906 5.875 19.9375C6.94141 19.9844 7.28125 19.9961 9.99609 19.9961C12.7109 19.9961 13.0508 19.9844 14.1172 19.9375C15.1797 19.8906 15.9102 19.7188 16.543 19.4727C17.1992 19.2188 17.7578 18.875 18.3125 18.3203C18.8672 17.7656 19.2109 17.207 19.4648 16.5508C19.7109 15.9141 19.8828 15.1875 19.9297 14.125C19.9766 13.0586 19.9883 12.7188 19.9883 10.0039C19.9883 7.28906 19.9766 6.94922 19.9297 5.88281C19.8828 4.82031 19.7109 4.08984 19.4648 3.45703C19.2188 2.79297 18.8789 2.23438 18.3203 1.67969C17.7656 1.125 17.207 0.78125 16.5508 0.527344C15.9141 0.28125 15.1875 0.109375 14.125 0.0625C13.0547 0.0117188 12.7148 0 10 0Z" fill="white"/>
                    <path d="M10 4.86328C7.16406 4.86328 4.86328 7.16406 4.86328 10C4.86328 12.8359 7.16406 15.1367 10 15.1367C12.8359 15.1367 15.1367 12.8359 15.1367 10C15.1367 7.16406 12.8359 4.86328 10 4.86328ZM10 13.332C8.16016 13.332 6.66797 11.8398 6.66797 10C6.66797 8.16016 8.16016 6.66797 10 6.66797C11.8398 6.66797 13.332 8.16016 13.332 10C13.332 11.8398 11.8398 13.332 10 13.332Z" fill="white"/>
                    <path d="M16.5391 4.66016C16.5391 5.32422 16 5.85938 15.3398 5.85938C14.6758 5.85938 14.1406 5.32031 14.1406 4.66016C14.1406 3.99609 14.6797 3.46094 15.3398 3.46094C16 3.46094 16.5391 4 16.5391 4.66016Z" fill="white"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_1_1527">
                      <rect width="20" height="20" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
              </a>
              <a href="#" aria-label="TikTok" className="hover:opacity-80 transition-opacity">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M14.2271 0H10.8565V13.6232C10.8565 15.2464 9.56016 16.5797 7.94689 16.5797C6.33362 16.5797 5.03725 15.2464 5.03725 13.6232C5.03725 12.029 6.30481 10.7246 7.86048 10.6667V7.24639C4.43227 7.30433 1.66667 10.1159 1.66667 13.6232C1.66667 17.1594 4.48989 20 7.97571 20C11.4615 20 14.2847 17.1304 14.2847 13.6232V6.63767C15.5523 7.56522 17.1079 8.11594 18.75 8.14494V4.72464C16.2149 4.63768 14.2271 2.55072 14.2271 0Z" fill="white"/>
                </svg>
              </a>
              <a href="#" aria-label="Facebook" className="hover:opacity-80 transition-opacity">
                <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <g clipPath="url(#clip0_1_1529)">
                    <path d="M10 0C4.4772 0 0 4.4772 0 10C0 14.6896 3.2288 18.6248 7.5844 19.7056V13.056H5.5224V10H7.5844V8.6832C7.5844 5.2796 9.1248 3.702 12.4664 3.702C13.1 3.702 14.1932 3.8264 14.6404 3.9504V6.7204C14.4044 6.6956 13.9944 6.6832 13.4852 6.6832C11.8456 6.6832 11.212 7.3044 11.212 8.9192V10H14.4784L13.9172 13.056H11.212V19.9268C16.1636 19.3288 20.0004 15.1128 20.0004 10C20 4.4772 15.5228 0 10 0Z" fill="white"/>
                  </g>
                  <defs>
                    <clipPath id="clip0_1_1529">
                      <rect width="20" height="20" fill="white"/>
                    </clipPath>
                  </defs>
                </svg>
              </a>
            </div>
          </div>

          {/* Navigation links */}
          <nav className="flex items-center gap-9 opacity-70 max-md:flex-wrap max-md:justify-start max-sm:flex-col max-sm:gap-4 max-sm:text-left max-sm:items-start max-md:mt-4">
            <a href="#" className="text-white text-xs font-normal leading-[14.4px] tracking-[-0.24px] hover:opacity-80 transition-opacity">
              Contact us
            </a>
            <a href="#" className="text-white text-xs font-normal leading-[14.4px] tracking-[-0.24px] hover:opacity-80 transition-opacity">
              Blog
            </a>
            <a href="#" className="text-white text-xs font-normal leading-[14.4px] tracking-[-0.24px] hover:opacity-80 transition-opacity">
              Terms &amp; conditions
            </a>
            <a href="#" className="text-white text-xs font-normal leading-[14.4px] tracking-[-0.24px] hover:opacity-80 transition-opacity">
              Privacy policy
            </a>
          </nav>

          {/* Copyright */}
          <div className="text-white text-right text-xs font-normal leading-[14.4px] tracking-[-0.24px] opacity-70 max-sm:text-left max-md:mt-4">
            © 2025 Wyatt. All rights reserved.
          </div>
        </div>
      </div>
    </footer>
  );
};
