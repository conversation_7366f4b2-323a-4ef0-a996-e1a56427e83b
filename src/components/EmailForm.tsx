import React, { useState } from 'react';

interface EmailFormProps {
  placeholder?: string;
  buttonText?: string;
  className?: string;
  onSubmit?: (url: string) => void;
}

export const EmailForm: React.FC<EmailFormProps> = ({
  placeholder = "Yoursite.com",
  buttonText = "See it in action",
  className = "",
  onSubmit
}) => {
  const [url, setUrl] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!url.trim()) return;

    setIsSubmitting(true);
    try {
      if (onSubmit) {
        await onSubmit(url);
      } else {
        // Default behavior - redirect to signup page
        console.log('URL submitted:', url);
        // Store the URL in localStorage so it can be accessed later if needed
        localStorage.setItem('submittedUrl', url);
        // Redirect to signup page
        window.location.href = '/signup';
      }
      setUrl('');
    } catch (error) {
      console.error('Error submitting URL:', error);
      alert('Something went wrong. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <form 
      onSubmit={handleSubmit} 
      className={`flex justify-between items-center bg-white p-3 rounded-lg max-md:flex-col max-md:gap-3 max-md:p-4 max-sm:p-3 max-sm:gap-3 ${className}`}
    >
      <div className="flex justify-center items-center gap-2.5 flex-[1_0_0] px-2 py-0 max-md:w-full max-sm:px-1">
        <input 
          type="text" 
          value={url} 
          onChange={e => setUrl(e.target.value)} 
          placeholder={placeholder} 
          className="flex-[1_0_0] text-black text-lg font-normal leading-[23px] tracking-[-0.36px] opacity-50 bg-transparent border-none outline-none placeholder:opacity-50 max-sm:text-base max-sm:leading-[20px] max-sm:tracking-[-0.32px]" 
          required 
          disabled={isSubmitting} 
        />
      </div>
      <button 
        type="submit" 
        disabled={isSubmitting || !url.trim()} 
        className="flex justify-center items-center gap-2 px-6 py-3 bg-black text-white text-lg font-normal leading-[23px] tracking-[-0.36px] rounded hover:bg-gray-800 transition-colors disabled:opacity-50 max-md:w-full max-sm:text-base max-sm:leading-[20px] max-sm:tracking-[-0.32px] max-sm:px-4 max-sm:py-2.5"
      >
        {isSubmitting ? 'Loading...' : buttonText}
      </button>
    </form>
  );
};
