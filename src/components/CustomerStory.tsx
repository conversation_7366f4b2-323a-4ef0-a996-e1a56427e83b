
import React from 'react';

export const CustomerStory: React.FC = () => {
  return (
    <section className="flex items-center gap-0 border w-[1072px] h-[687px] rounded-2xl border-solid border-[#24242F] mx-auto overflow-hidden max-md:flex-col max-md:w-[90%] max-md:h-auto max-md:gap-0 max-sm:w-[95%]">
      <div className="flex h-[687px] flex-col justify-between items-start flex-[1_0_0] px-12 py-12 bg-[#2A2A2A] max-md:w-full">
        <div className="flex items-center gap-3 self-stretch">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g clipPath="url(#clip0_1_1416)">
              <path d="M21 3C21.552 3 22 3.448 22 4V18C22 18.552 21.552 19 21 19H6.455L2 22.5V4C2 3.448 2.448 3 3 3H21ZM10.962 8.1L10.515 7.412C8.728 8.187 7.5 9.755 7.5 11.505C7.5 12.5 7.777 13.114 8.292 13.661C8.616 14.005 9.129 14.25 9.666 14.25C10.632 14.25 11.416 13.466 11.416 12.5C11.416 11.58 10.705 10.839 9.802 10.755C9.642 10.74 9.478 10.743 9.323 10.765V10.673C9.329 10.251 9.415 9.04 10.777 8.207L10.962 8.1L10.515 7.412L10.962 8.1ZM15.515 7.412C13.728 8.187 12.5 9.755 12.5 11.505C12.5 12.5 12.777 13.114 13.292 13.661C13.616 14.005 14.129 14.25 14.666 14.25C15.632 14.25 16.416 13.466 16.416 12.5C16.416 11.58 15.705 10.839 14.802 10.755C14.642 10.74 14.478 10.743 14.323 10.765C14.323 10.452 14.294 9.003 15.962 8.1L15.515 7.412Z" fill="white"/>
            </g>
            <defs>
              <clipPath id="clip0_1_1416">
                <rect width="24" height="24" fill="white"/>
              </clipPath>
            </defs>
          </svg>
          <div className="text-white text-base font-medium leading-6">Customer stories</div>
        </div>
        
        <div className="flex flex-col justify-end items-start gap-6 flex-[1_0_0] self-stretch">
          <h2 className="self-stretch text-white text-5xl font-medium leading-[56px] tracking-[-0.48px] max-sm:text-[32px] max-sm:leading-10">
            Faster experimentation, faster growth
          </h2>
          <p className="self-stretch text-[rgba(255,255,255,0.70)] text-xl font-normal leading-8 tracking-[-0.4px] max-sm:text-base max-sm:leading-6">
            Most teams run 1-2 tests per month. Variant runs dozens — automatically.
          </p>
        </div>
      </div>

      <div className="w-[504px] h-[687px] shrink-0 relative bg-[#F5F5F5] max-md:w-full max-md:max-w-[500px]">
        <div className="w-[369px] text-[#0855A2] text-[32px] font-medium leading-9 tracking-[-0.32px] absolute h-[108px] left-[45px] top-[37px]">
          Sparrow increased conversion rate by 8.7% in 90 days.
        </div>
        
        <div className="w-[352px] text-[#070018] text-[28px] italic font-normal leading-9 tracking-[-0.56px] absolute h-[72px] left-[45px] top-[445px]">
          "Variant is a game-changer for AB testing"
        </div>
        
        <div className="text-black text-base font-semibold leading-5 tracking-[-0.32px] absolute w-24 h-5 left-[45px] top-[540px]">
          Raymond Lei
        </div>
        
        <div className="text-black text-xs font-normal leading-5 tracking-[-0.24px] absolute w-[97px] h-5 left-[45px] top-[562px]">
          Founder, Sparrow
        </div>

        {/* Chart */}
        <div className="w-[504px] h-[310px] shrink-0 absolute left-0 top-[98px]">
          <svg width="504" height="262" viewBox="0 0 504 262" fill="none" xmlns="http://www.w3.org/2000/svg" className="absolute left-0 top-12">
            <path d="M13.153 80.4172L0 75.4591V262H504V0L491.404 3.21649C489.315 3.74995 487.326 4.61743 485.514 5.78542L475.738 12.0855C473.599 13.4642 471.216 14.4222 468.718 14.9083L460.572 16.4931C458.048 16.9843 455.452 16.9843 452.928 16.4931L448.196 15.5725C443.549 14.6685 438.733 15.4404 434.602 17.7511L429.13 20.812C426.575 22.2416 423.736 23.0929 420.816 23.306L409.5 24.1316L396.646 25.7728C394.726 26.018 392.78 25.9832 390.869 25.6696L384.33 24.5962C380.213 23.9203 375.988 24.5479 372.245 26.3912L364.594 30.1593C363.038 30.9256 361.389 31.4864 359.688 31.8276L346.5 34.4737L332.556 36.6779C331.355 36.8679 330.173 37.167 329.025 37.5717L316.952 41.8293C315.654 42.2869 314.313 42.6095 312.949 42.7919L302.144 44.2371C300.225 44.4939 298.278 44.4707 296.366 44.1683L287.928 42.8346C285.012 42.3736 282.03 42.5634 279.196 43.3906L269.375 46.2567C268.294 46.5723 267.241 46.9787 266.228 47.4714L256.492 52.2068C253.543 53.6415 250.286 54.3293 247.008 54.2097L237.692 53.8699C236.732 53.8348 235.776 53.7307 234.83 53.5583L225.618 51.8779C222.262 51.2658 218.805 51.5216 215.575 52.6212L204.75 56.307L189 59.3713L175.657 61.3184C174.058 61.5517 172.437 61.5905 170.829 61.4341L160.638 60.4427C158.559 60.2404 156.461 60.3649 154.42 60.8115L145.071 62.8579C142.873 63.3391 140.609 63.4461 138.375 63.1744L128.898 62.0219C126.976 61.7883 125.031 61.8346 123.123 62.1595L112.042 64.0458C110.85 64.2487 109.678 64.5594 108.542 64.9739L96.4331 69.3913C95.1478 69.8602 93.8177 70.1961 92.4638 70.3936L81.64 71.973C79.7235 72.2527 77.7765 72.2527 75.86 71.973L69.6247 71.0632C65.3253 70.4358 60.9385 71.2245 57.1269 73.3102L49.4623 77.5044C47.9932 78.3082 46.4291 78.9245 44.8066 79.3389L34.7783 81.8997C32.6082 82.4539 30.3604 82.6398 28.1287 82.4499L18.5154 81.6316C16.6806 81.4754 14.8761 81.0668 13.153 80.4172Z" fill="url(#paint0_radial_1_1430)"/>
            <defs>
              <radialGradient id="paint0_radial_1_1430" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(398.704 262) rotate(-101.125) scale(226.874 703.88)">
                <stop stopColor="#6492FF" stopOpacity="0.2"/>
                <stop offset="1" stopColor="#3062D9" stopOpacity="0"/>
              </radialGradient>
            </defs>
          </svg>

          <svg width="419" height="139" viewBox="0 0 419 139" fill="none" xmlns="http://www.w3.org/2000/svg" className="absolute left-[23px] top-[35px]">
            <path d="M1 138L14.239 115.402C15.9902 112.413 18.9151 110.296 22.3016 109.565L23.6363 109.278C27.4776 108.449 31.453 109.901 33.8562 113.011V113.011C38.2119 118.646 46.8554 118.195 50.6013 112.137L55.7949 103.738L67.8274 88.8199C68.4267 88.0769 69.1274 87.4219 69.9091 86.8742L70.1619 86.697C75.7831 82.7578 83.6106 85.5511 85.468 92.1591L86.8587 97.1071C89.1363 105.21 100.57 105.358 103.056 97.3165L104.328 93.2009C105.729 88.6719 111.559 87.4418 114.669 91.019V91.019C117.461 94.23 122.609 93.6295 124.587 89.862L126.559 86.1051C129.68 80.1608 137.993 79.6434 141.827 85.1547L142.621 86.2963C144.877 89.5394 148.861 91.1091 152.723 90.2764L159.282 88.8621L170.422 85.6594C171.617 85.3159 172.777 84.8619 173.887 84.3032L178.328 82.0691C182.724 79.8574 187.78 79.3427 192.532 80.6234L196.059 81.574C197.409 81.9379 198.794 82.1593 200.19 82.2346L203.989 82.4394C208.543 82.6849 213.044 81.3677 216.748 78.706L222.199 74.7879C223.371 73.9458 224.63 73.2329 225.955 72.6616L230.841 70.5542C234.775 68.858 239.149 68.4684 243.32 69.4428L245.764 70.0137C248.446 70.6402 251.231 70.688 253.933 70.154V70.154C259.536 69.0465 264.366 65.5243 267.133 60.5275L271.401 52.8203C274.141 47.8712 278.848 44.313 284.357 43.0262L288.641 42.0256L298.262 39.173C300.456 38.5227 302.745 38.2533 305.029 38.3764L312.668 38.7882C313.895 38.8543 315.114 39.0334 316.309 39.3232L323.258 41.0089C326.02 41.679 328.893 41.753 331.687 41.226L340.385 39.5849L345.169 39.0691C350.393 38.5059 355.185 35.9116 358.513 31.8459L360.841 29.0009C364.202 24.8944 369.467 22.8459 374.719 23.601V23.601C377.638 24.0205 380.615 23.5792 383.286 22.3312L389.165 19.5848C391.127 18.668 392.928 17.4388 394.496 15.9451L402.125 8.68042C404.065 6.83309 406.355 5.39394 408.862 4.44825L418 1" stroke="#4185C9" strokeWidth="2"/>
          </svg>

          {/* Chart Labels */}
          <div className="w-[393px] h-[287px] shrink-0 absolute left-[91px] top-0">
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-0 top-[277px]">15</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[58px] top-[277px]">Dec</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[121px] top-[277px]">15</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[178px] top-[277px]">Jan</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[241px] top-[277px]">15</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[297px] top-[277px]">Feb</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[375px] top-60">0%</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[375px] top-36">4%</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[375px] top-48">2%</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[375px] top-12">8%</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[375px] top-24">6%</div>
            <div className="text-[#9690AA] text-[9px] font-normal absolute left-[375px] top-0">10%</div>
          </div>
        </div>
      </div>
    </section>
  );
};
